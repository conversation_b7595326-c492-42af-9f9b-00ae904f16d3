"use client";

import Breadcrumb, { useDatabaseBreadcrumb } from "@/components/Breadcrumb";

interface DetailPageBreadcrumbProps {
  database: string;
  itemTitle: string;
}

export default function DetailPageBreadcrumb({ database, itemTitle }: DetailPageBreadcrumbProps) {
  const { breadcrumbItems, loading } = useDatabaseBreadcrumb(database, itemTitle);

  if (loading) {
    return (
      <nav className="flex items-center space-x-0.5 text-xs text-gray-500 py-1.5 leading-tight bg-gray-50 border-b border-gray-100">
        <div className="container mx-auto px-4 flex items-center space-x-0.5">
          <div className="h-3 w-10 bg-gray-200 rounded animate-pulse"></div>
          <span className="text-gray-400 mx-1.5">›</span>
          <div className="h-3 w-14 bg-gray-200 rounded animate-pulse"></div>
          <span className="text-gray-400 mx-1.5">›</span>
          <div className="h-3 w-16 bg-gray-200 rounded animate-pulse"></div>
          <span className="text-gray-400 mx-1.5">›</span>
          <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </nav>
    );
  }

  return <Breadcrumb items={breadcrumbItems} />;
}
